import { should } from 'chai';
import { afterEach, beforeEach, describe, it } from 'mocha';
import { DateTime, IContext } from 'edana-microservice';

import sinon, { SinonSandbox, SinonStub } from 'sinon';
import { removeExtraSpaces } from '../utils';

import conversationsCount from '../../endpoints/conversationList/conversationsCount';

should();

describe(`conversations count endpoint`, () => {
  let sandbox: SinonSandbox;
  let context: IContext;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    context = ({
      connection: {
        commit: sandbox.stub(),
        execute: sandbox
          .stub()
          .returns(Promise.resolve({ rows: [{ COUNT: 4 }] })),
      },
    } as unknown) as IContext;
  });

  afterEach(() => {
    sandbox.reset();
    sandbox.restore();
  });

  it.skip(`should call execute with correct arguments`, async () => {
    const res = await conversationsCount(context, {
      tenantId: 1,
      userId: 4,
      lastActivity: DateTime.utc(2010, 11, 11),
      organisationIds: [1, 2, 3],
      searchQuery: 'search',
      searchQueryMode: ['MESSAGES'],
    });

    const args = (context.connection.execute as SinonStub).getCall(0).args;
    removeExtraSpaces(args[0]).should.deep.equal(
      removeExtraSpaces(`
                SELECT COUNT(ID) AS COUNT
        FROM EC_CONVERSATION
        WHERE (
            ID IN (
              SELECT CONVERSATION_ID 
              FROM EC_CONVERSATION_ITEM
              WHERE LOWER(CONTENT) LIKE :like_ConversationItem_content AND TENANT_ID = :ConversationItem_tenantId AND STATUS = :ConversationItem_status
            )    
          ) AND 
            ID IN ( 
              SELECT C.ID FROM EC_CONVERSATION C 
              LEFT JOIN EC_CONVERSATION_ITEM CI 
              ON C.ID = CI.CONVERSATION_ID AND CI.STATUS = :status 
              WHERE C.TENANT_ID = :tenantId 
              AND ( CI.UPDATED_AT >= :lastActivity 
                      OR CI.CREATED_AT >= :lastActivity 
                      OR C.CREATED_AT >= :lastActivity )
            ) 
     AND (ORGANISATION_ID IN (:Conversation_organisationId0, :Conversation_organisationId1, :Conversation_organisationId2)) AND TENANT_ID = :Conversation_tenantId AND STATUS != :not_Conversation_status AND 
            ID IN (
              SELECT CONVERSATION_ID 
              FROM EC_CONVERSATION_PARTICIPANT 
              WHERE PERSON_ID = :ConversationParticipant_personId AND TENANT_ID = :ConversationParticipant_tenantId AND STATUS = :ConversationParticipant_status
            ) 
         AND TENANT_ID = :Conversation_tenantId AND STATUS != :not_Conversation_status

    `),
    );

    args[1].should.be.deep.equal({
      tenantId: 1,
      status: 1,
      ConversationItem_status: 1,
      ConversationItem_tenantId: 1,
      ConversationParticipant_personId: 4,
      ConversationParticipant_status: 1,
      ConversationParticipant_tenantId: 1,
      Conversation_organisationId0: 1,
      Conversation_organisationId1: 2,
      Conversation_organisationId2: 3,
      Conversation_tenantId: 1,
      lastActivity: DateTime.utc(2010, 11, 11).toJSDate(),
      like_ConversationItem_content: '%search%',
      not_Conversation_status: 4,
    });
    res.should.equal(4);
  });
});
