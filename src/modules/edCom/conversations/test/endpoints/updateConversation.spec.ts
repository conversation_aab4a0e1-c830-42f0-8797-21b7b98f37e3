import { should } from 'chai';
import { afterEach, beforeEach, describe, it } from 'mocha';
import { IContext } from 'edana-microservice';

import sinon, { SinonSandbox, SinonStub } from 'sinon';

import * as m from '../entities/mockData';

import Conversation from '../../store/entities/Conversation';
import utils from '../../store/services/ConversationListService/utils';
import updateConversation from '../../endpoints/conversationList/updateConversation';

should();

describe(`updateConversation endpoint`, () => {
  let sandbox: SinonSandbox;
  let context: IContext;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    context = ({
      connection: {
        commit: sandbox.stub(),
        execute: sandbox.stub(),
      },
    } as unknown) as IContext;
    sandbox
      .stub(Conversation, 'update')
      .returns(Promise.resolve(m.conversation.cooked));
    sandbox.stub(utils, 'manageParticipants');
    sandbox.stub(utils, 'manageThumbnail');
  });

  afterEach(() => {
    sandbox.reset();
    sandbox.restore();
  });

  it.skip(`should create conversation`, async () => {
    const res = await updateConversation(context, {
      tenantId: 2,
      id: 3,
      body: {
        params: {
          personEntityAllocationId: 1,
          organisationId: 3,
          topic: 'topic',
          status: 'ACTIVE',
          userId: 4,

          participantsAdded: [1, 2, 3],
          participantsRemoved: [4, 5, 6],
        } as any,
      },
    });

    (Conversation.update as SinonStub).getCall(0).args.should.deep.equal([
      context,
      {
        id: 3,
        tenantId: 2,

        dateFrom: undefined,
        dateTo: undefined,
        organisationId: 3,
        personEntityAllocationId: 1,
        status: 'ACTIVE',
        topic: 'topic',
        typePartCount: undefined,
      },
    ]);

    (utils.manageThumbnail as SinonStub).getCall(0).args.should.be.deep.equal([
      context,
      {
        conversationId: 1,
        logoAttachment: undefined,
        tenantId: 2,
      },
    ]);
    (utils.manageParticipants as SinonStub)
      .getCall(0)
      .args.should.be.deep.equal([
        context.connection,
        2,
        1,
        { addedParticipantIds: [1, 2, 3], removedParticipantIds: [4, 5, 6] },
      ]);

    (context.connection.commit as SinonStub).called.should.be.true;

    res.should.be.deep.equal(m.conversation.cooked);
  });
});
