import { should } from 'chai';
import { afterEach, beforeEach, describe, it } from 'mocha';
import { IContext } from 'edana-microservice';

import sinon, { SinonSandbox, SinonStub } from 'sinon';

import * as m from '../entities/mockData';

import deleteConversation from '../../endpoints/conversationList/deleteConversation';
import Conversation from '../../store/entities/Conversation';

should();

describe(`deleteConversation endpoint`, () => {
  let sandbox: SinonSandbox;
  let context: IContext;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    context = ({
      connection: {
        commit: sandbox.stub(),
        execute: sandbox.stub(),
      },
    } as unknown) as IContext;
    sandbox
      .stub(Conversation, 'update')
      .returns(Promise.resolve(m.conversation.cooked));
  });

  afterEach(() => {
    sandbox.reset();
    sandbox.restore();
  });

  it.skip(`should update conversation's status to DELETED`, async () => {
    const res = await deleteConversation(context, {
      id: 1,
      tenantId: 2,
      userId: 3,
    });

    (Conversation.update as SinonStub)
      .getCall(0)
      .args.should.deep.equal([
        context.connection,
        { id: 1, tenantId: 2, status: 'DELETED', ownerPersonId: 3 },
      ]);

    (context.connection.commit as SinonStub).called.should.be.true;

    res.should.be.deep.equal({ id: 1, deleted: true });
  });
});
