import { DateTime, IWIthId, IWithType } from 'edana-microservice';

import { TConversationStatus } from '../model/ConversationStatus';
import { TConversationTypePartCount } from '../model/ConversationTypePartCount';

interface IConversation extends IWithType, IWIthId {
  tenantId: number;
  status: TConversationStatus;
  personEntityAllocationId: number;
  organisationId?: number;
  topic: string;
  ownerPersonId: number;
  moduleAreaId?: number;
  referenceId?: number;
  dateFrom?: DateTime;
  dateTo?: DateTime;
  createdBy: number;
  createdAt: DateTime;
  updatedBy: number;
  updatedAt: DateTime;
  typePartCount: TConversationTypePartCount;
}

export default IConversation;
