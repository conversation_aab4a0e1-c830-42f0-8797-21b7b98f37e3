import { IContext, DateTime } from 'edana-microservice';
import { first, last } from 'lodash';

import ConversationParticipant from '../../entities/ConversationParticipant';

import ConversationNote from '../../entities/ConversationNote';
import ConversationNoteLastRead from '../../entities/ConversationNoteLastRead';

interface IReadConversationNote {
  tenantId: number;
  conversationNoteIds: number[];
  userId: number;
}

export default async function readConversationNote(
  context: IContext,
  { tenantId, conversationNoteIds, userId }: IReadConversationNote,
) {
  const { conversationId } = await ConversationNote.findBy(context.connection, {
    tenantId,
    id: first(conversationNoteIds),
  });

  const {
    id: conversationParticipantId,
  } = await ConversationParticipant.findBy(context.connection, {
    tenantId,
    personId: userId,
    conversationId,
  });

  const exists = await ConversationNoteLastRead.findBy(
    context.connection,
    {
      conversationId,
      conversationParticipantId,
      tenantId,
    },
    {},
    { allowNull: true },
  );
  if (exists) {
    await ConversationNoteLastRead.update(context.connection, {
      id: exists.id,
      tenantId,
      conversationId,
      conversationParticipantId,
      lastConversationNoteId: last(conversationNoteIds),
      conversationNoteUnreadCount: 0,
      readAt: DateTime.utc(),
    });
  } else {
    await ConversationNoteLastRead.create(context.connection, {
      tenantId,
      conversationId,
      conversationParticipantId,
      lastConversationNoteId: last(conversationNoteIds),
      conversationNoteUnreadCount: 0,
      readAt: DateTime.utc(),
    });
  }

  await context.connection.commit();
  return true;
}
