import { DateTime, IContext, IAttachFileInfo } from 'edana-microservice';
import ConversationNote from '../../entities/ConversationNote';

import u from './utils';
import ConversationParticipant from '../../entities/ConversationParticipant';
import { ACTIVE } from '../../../../../../model/Status';
import ConversationNoteLastRead from '../../entities/ConversationNoteLastRead';
import Conversation from '../../entities/Conversation';

interface ICreateConversationNote {
  tenantId: number;
  organisationGroupId: number;
  conversationId: number;
  userId: number;
  content: string;
  attachments: IAttachFileInfo[];
}

export default async function createConversationNote(
  context: IContext,
  {
    tenantId,
    attachments,
    content,
    userId,
    organisationGroupId,
    conversationId,
  }: ICreateConversationNote,
) {
  const {
    id: conversationParticipantId,
  } = await ConversationParticipant.findBy(context.connection, {
    personId: userId,
  });

  const participants = await ConversationParticipant.loadBy(
    context.connection,
    {
      tenantId,
      conversationId,
      personId: { not: userId },
      status: ACTIVE,
    },
  );

  const res = await ConversationNote.create(context.connection, {
    tenantId,
    content,
    conversationId,
    conversationParticipantId,
    createdAt: DateTime.utc(),
    updatedAt: DateTime.utc(),
  });

  for (const participant of participants) {
    const exists = await ConversationNoteLastRead.findBy(
      context.connection,
      {
        conversationId,
        conversationParticipantId: participant.id,
        tenantId,
      },
      {},
      { allowNull: true },
    );
    if (exists) {
      await ConversationNoteLastRead.update(context.connection, {
        id: exists.id,
        tenantId,
        conversationNoteUnreadCount: exists.conversationNoteUnreadCount + 1,
        readAt: DateTime.utc(),
      });
    } else {
      await ConversationNoteLastRead.create(context.connection, {
        tenantId,
        conversationId,
        conversationParticipantId: participant.id,
        // TODO: Needed to be replaced with NULL
        lastConversationNoteId: 21,
        conversationNoteUnreadCount: 1,
        readAt: DateTime.utc(),
      });
    }
  }

  await u.manageConversationNoteAttachments(context, {
    tenantId,
    attachments,
    conversationNoteId: res.id,
    organisationGroupId,
  });

  await Conversation.update(context, {
    id: conversationId,
    tenantId,
    updatedAt: DateTime.utc(),
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    skipCheckAccess: true,
  });

  await context.connection.commit();

  return res;
}
