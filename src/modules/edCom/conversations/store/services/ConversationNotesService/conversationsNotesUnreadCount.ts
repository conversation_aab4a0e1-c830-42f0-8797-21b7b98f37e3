import { IContext } from 'edana-microservice';
import { isEmpty, map } from 'lodash';

import ConversationParticipant from '../../entities/ConversationParticipant';
import ConversationNoteLastRead from '../../entities/ConversationNoteLastRead';

interface IConversationsNotesUnreadCount {
  tenantId: number;
  conversationIds: number[];
  userId: number;
}

export default async function conversationsNotesUnreadCount(
  context: IContext,
  { tenantId, conversationIds, userId }: IConversationsNotesUnreadCount,
) {
  const conversationParticipants = await ConversationParticipant.loadBy(
    context.connection,
    {
      tenantId,
      personId: userId,
      ...(!isEmpty(conversationIds) ? { conversationId: conversationIds } : {}),
    },
  );
  const response = await ConversationNoteLastRead.loadBy(context.connection, {
    tenantId,
    conversationParticipantId: map(conversationParticipants, 'id'),
    conversationId: map(conversationParticipants, 'conversationId'),
  });

  return map(response, ({ conversationNoteUnreadCount, conversationId }) => ({
    unreadCount: conversationNoteUnreadCount,
    conversationId,
  }));
}
