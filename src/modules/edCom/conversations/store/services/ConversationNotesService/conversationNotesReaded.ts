import { IContext } from 'edana-microservice';

import ConversationParticipant from '../../entities/ConversationParticipant';
import ConversationNoteLastRead from '../../entities/ConversationNoteLastRead';

interface IConversationNotesReaded {
  tenantId: number;
  conversationId: number;
  userId: number;
}

export default async function conversationNotesReaded(
  context: IContext,
  { tenantId, conversationId, userId }: IConversationNotesReaded,
) {
  const {
    id: conversationParticipantId,
  } = await ConversationParticipant.findBy(context.connection, {
    tenantId,
    personId: userId,
    conversationId,
  });

  const response = await ConversationNoteLastRead.findBy(
    context.connection,
    {
      tenantId,
      conversationParticipantId,
      conversationId,
    },
    {},
    { allowNull: true },
  );

  return response;
}
