import { IContext, DeleteResponse } from 'edana-microservice';
import Conversation from '../../entities/Conversation';
import ConversationStatus from '../../../model/ConversationStatus';

interface IDeleteConversationArgs {
  id: number;
  tenantId: number;
  userId: number;
  isHardDelete?: boolean;
}

export default async function deleteConversation(
  context: IContext,
  { id, tenantId, userId, isHardDelete = false }: IDeleteConversationArgs,
) {
  if (isHardDelete) {
    await Conversation.deleteBy(context.connection, { id, tenantId });
  } else {
    await Conversation.update(context.connection, {
      id,
      tenantId,
      ownerPersonId: userId,
      status: ConversationStatus.DELETED,
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      userId,
    });
  }

  await context.connection.commit();
  return new DeleteResponse(id);
}
