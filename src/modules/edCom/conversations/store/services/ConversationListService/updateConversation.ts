import { IContext, DateTime, IAttachFileInfo } from 'edana-microservice';
import { TConversationStatus } from '../../../model/ConversationStatus';
import Conversation from '../../entities/Conversation';
import u from './utils';
import { TConversationTypePartCount } from '../../../model/ConversationTypePartCount';

interface IUpdateConversationArgs {
  id: number;
  topic: string;
  status: TConversationStatus;
  dateFrom?: DateTime;
  dateTo?: DateTime;
  organisationId: number;
  personEntityAllocationId: number;
  tenantId: number;
  userId: number;
  typePartCount: TConversationTypePartCount;

  logoAttachment?: IAttachFileInfo;
  participantsAdded: number[];
  participantsRemoved: number[];
}

export default async function updateConversation(
  context: IContext,
  {
    id,
    tenantId,
    personEntityAllocationId,
    organisationId,
    topic,
    dateFrom,
    dateTo,
    status,

    logoAttachment,
    participantsAdded,
    participantsRemoved,
    typePartCount,
  }: IUpdateConversationArgs,
) {
  const conversation = await Conversation.update(context, {
    id,
    tenantId,
    personEntityAllocationId,
    organisationId,
    topic,
    dateFrom,
    dateTo,
    status,
    typePartCount,
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    userId: context.userId,
  });

  await u.manageParticipants(context.connection, tenantId, conversation.id, {
    addedParticipantIds: participantsAdded,
    removedParticipantIds: participantsRemoved,
  });

  await u.manageThumbnail(context, {
    conversationId: conversation.id,
    logoAttachment,
    tenantId,
  });
  await context.connection.commit();

  return conversation;
}
