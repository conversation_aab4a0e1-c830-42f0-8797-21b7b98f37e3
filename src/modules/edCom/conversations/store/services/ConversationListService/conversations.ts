import { DateTime, IContext } from 'edana-microservice';
import { isEmpty } from 'lodash';
import { TSearchQueryMode } from '../../../model/SearchQueryMode';
import Conversation from '../../entities/Conversation';
import u from './utils';
interface IConversationsArgs {
  tenantId: number;
  first: number;
  count: number;
  searchQuery: string;
  searchQueryMode: TSearchQueryMode[];
  organisationIds: number[];
  lastActivity?: DateTime;
  userId: number;
}

export default async function conversations(
  context: IContext,
  {
    first,
    count,
    searchQuery,
    searchQueryMode,
    organisationIds,
    lastActivity,
    tenantId,
    userId,
  }: IConversationsArgs,
) {
  if (isEmpty(searchQueryMode)) return [];
  const { whereClause, params } = u.cookConversationsConstraints({
    searchQuery,
    searchQueryMode,
    organisationIds,
    lastActivity,
    tenantId,
    userId,
  });

  const where = isEmpty(whereClause)
    ? ''
    : `WHERE ${whereClause.join(' AND ')}`;

  const query = `
    SELECT *
FROM ${Conversation.table}
${where}
ORDER BY COALESCE(UPDATED_AT, CREATED_AT) DESC
OFFSET :first ROWS FETCH NEXT :count ROWS ONLY`;

  return await Conversation.loadWithQuery(context.connection, query, {
    ...params,
    first,
    count,
  });
}
