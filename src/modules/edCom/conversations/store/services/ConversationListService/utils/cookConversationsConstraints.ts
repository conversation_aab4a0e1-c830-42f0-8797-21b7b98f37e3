import { DateTime, actionHelpers } from 'edana-microservice';
import { isEmpty } from 'lodash';
import Status from '../../../../../../../model/Status';

import Conversation from '../../../entities/Conversation';
import ConversationItem from '../../../entities/ConversationItem';
import {
  TOPIC,
  MESSAGES,
  TSearchQueryMode,
} from '../../../../model/SearchQueryMode';
import ConversationParticipant from '../../../entities/ConversationParticipant';
import { HIDDEN, DELETED, mapping } from '../../../../model/ConversationStatus';

export default function getListConstraints({
  tenantId,
  searchQuery,
  searchQueryMode,
  lastActivity,
  organisationIds,
  userId,
}: IGetListConstraintsInput): { whereClause: string[]; params: any } {
  let whereClause: string[] = [];
  let _params = {};

  if (searchQuery) {
    let searchClause: string[] = [];
    if (searchQueryMode.includes(TOPIC)) {
      const { clauses, params } = actionHelpers.cookWhereClauses(
        Conversation.framework,
        {
          topic: { like: searchQuery },
          tenantId,
          status: { not: DELETED },
        },
      );
      searchClause = [...searchClause, clauses.join(' AND ')];
      _params = { ..._params, ...params };
    }

    if (searchQueryMode.includes(MESSAGES)) {
      const { clauses, params } = actionHelpers.cookWhereClauses(
        ConversationItem.framework,
        {
          content: { like: searchQuery },
          tenantId,
          status: Status.ACTIVE,
        },
      );
      searchClause.push(`
            ID IN (
              SELECT CONVERSATION_ID 
              FROM EC_CONVERSATION_ITEM
              WHERE ${clauses.join(' AND ')}
            )    
          `);
      _params = { ..._params, ...params };
    }

    whereClause = [...whereClause, `(${searchClause.join(' OR ')})`];
  }

  if (lastActivity) {
    whereClause.push(`
            ID IN ( 
              SELECT C.ID FROM EC_CONVERSATION C 
              LEFT JOIN EC_CONVERSATION_ITEM CI 
              ON C.ID = CI.CONVERSATION_ID AND CI.STATUS = :status 
              WHERE C.TENANT_ID = :tenantId 
              AND ( CI.UPDATED_AT >= :lastActivity 
                      OR CI.CREATED_AT >= :lastActivity 
                      OR C.CREATED_AT >= :lastActivity )
            ) 
    `);
    _params = {
      ..._params,
      tenantId,
      lastActivity: lastActivity.toJSDate(),
      status: Status.ACTIVE_ID,
    };
  }

  if (!isEmpty(organisationIds)) {
    const { clauses, params } = actionHelpers.cookWhereClauses(
      Conversation.framework,
      {
        organisationId: organisationIds,
        tenantId,
        status: { not: DELETED },
      },
    );

    whereClause = [...whereClause, ...clauses];
    _params = { ..._params, ...params };
  }

  if (userId) {
    const { clauses, params } = actionHelpers.cookWhereClauses(
      ConversationParticipant.framework,
      {
        personId: userId,
        tenantId,
        status: Status.ACTIVE,
      },
    );
    whereClause.push(`
            ID IN (
              SELECT CONVERSATION_ID 
              FROM ${ConversationParticipant.table} 
              WHERE ${clauses.join(' AND ')}
            ) 
        `);
    _params = { ..._params, ...params };
  }

  // Exclude hidden conversations where ownerPersonId is not userId
  whereClause.push(`
    NOT (STATUS = :hiddenStatus AND OWNER_PERSON_ID != :userId)
  `);
  _params = {
    ..._params,
    hiddenStatus: mapping[HIDDEN],
    userId,
  };

  const { params, clauses } = actionHelpers.cookWhereClauses(
    Conversation.framework,
    {
      tenantId,
      status: { not: DELETED },
    },
  );

  return {
    whereClause: [...whereClause, ...clauses],
    params: { ..._params, ...params },
  };
}

export interface IGetListConstraintsInput {
  searchQuery?: string;
  searchQueryMode: TSearchQueryMode[];
  lastActivity?: DateTime;
  organisationIds: number[];
  tenantId: number;
  userId: number;
}
