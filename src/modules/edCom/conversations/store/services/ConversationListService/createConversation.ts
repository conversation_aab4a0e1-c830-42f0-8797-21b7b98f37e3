import { IContext, DateTime, IAttachFileInfo } from 'edana-microservice';
import { uniq } from 'lodash';
import { TConversationStatus } from '../../../model/ConversationStatus';
import Conversation from '../../entities/Conversation';
import u from './utils';
import { TConversationTypePartCount } from '../../../model/ConversationTypePartCount';

interface ICreateConversationArgs {
  topic: string;
  status: TConversationStatus;
  dateFrom?: DateTime;
  dateTo?: DateTime;
  organisationId: number;
  personEntityAllocationId: number;
  tenantId: number;
  userId: number;
  typePartCount: TConversationTypePartCount;

  logoAttachment?: IAttachFileInfo;
  participantsAdded: number[];
  participantsRemoved: number[];
}

export default async function createConversation(
  context: IContext,
  {
    tenantId,
    personEntityAllocationId,
    organisationId,
    topic,
    userId,
    dateFrom,
    dateTo,
    status,
    typePartCount,

    logoAttachment,
    participantsAdded,
    participantsRemoved,
  }: ICreateConversationArgs,
) {
  const conversation = await Conversation.create(context, {
    tenantId,
    personEntityAllocationId,
    organisationId,
    topic,
    ownerPersonId: userId,
    dateFrom,
    dateTo,
    createdBy: userId,
    createdAt: DateTime.utc(),
    updatedBy: userId,
    updatedAt: DateTime.utc(),
    status,
    typePartCount,
  });

  await u.manageParticipants(context.connection, tenantId, conversation.id, {
    addedParticipantIds: uniq(participantsAdded),
    removedParticipantIds: uniq(participantsRemoved),
  });

  await u.manageThumbnail(context, {
    conversationId: conversation.id,
    logoAttachment,
    tenantId,
  });
  await context.connection.commit();

  return conversation;
}
