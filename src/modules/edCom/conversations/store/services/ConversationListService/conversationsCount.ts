import { DateTime, IContext } from 'edana-microservice';
import { isEmpty } from 'lodash';
import { TSearchQueryMode } from '../../../model/SearchQueryMode';
import Conversation from '../../entities/Conversation';
import u from './utils';

interface IConversationsCountArgs {
  tenantId: number;
  searchQuery: string;
  searchQueryMode: TSearchQueryMode[];
  organisationIds: number[];
  lastActivity?: DateTime;
  userId: number;
}

export default async function conversationsCount(
  context: IContext,
  {
    searchQuery,
    searchQueryMode,
    organisationIds,
    lastActivity,
    tenantId,
    userId,
  }: IConversationsCountArgs,
): Promise<number> {
  if (searchQueryMode) return 0;
  const { whereClause, params } = u.cookConversationsConstraints({
    searchQuery,
    searchQueryMode,
    organisationIds,
    lastActivity,
    tenantId,
    userId,
  });

  const where = isEmpty(whereClause)
    ? ''
    : `WHERE ${whereClause.join(' AND ')}`;

  const res = await context.connection.execute<{ COUNT: number }>(
    `
        SELECT COUNT(ID) AS COUNT
        FROM ${Conversation.table}
        ${where}`,
    { ...params },
  );

  return (res.rows && res.rows[0].COUNT) || 0;
}
