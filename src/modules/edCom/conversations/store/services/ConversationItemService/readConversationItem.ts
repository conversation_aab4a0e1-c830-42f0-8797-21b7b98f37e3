import { IContext, DateTime } from 'edana-microservice';
import ConversationParticipant from '../../entities/ConversationParticipant';
import ConversationItem from '../../entities/ConversationItem';
import { first, last } from 'lodash';
import ConversationItemLastRead from '../../entities/ConversationItemLastRead';

interface IReadConversationItem {
  tenantId: number;
  conversationItemIds: number[];
  userId: number;
}

export default async function readConversationItem(
  context: IContext,
  { tenantId, conversationItemIds, userId }: IReadConversationItem,
) {
  const { conversationId } = await ConversationItem.findBy(context.connection, {
    tenantId,
    id: first(conversationItemIds),
  });

  const {
    id: conversationParticipantId,
  } = await ConversationParticipant.findBy(context.connection, {
    tenantId,
    personId: userId,
    conversationId,
  });

  const exists = await ConversationItemLastRead.findBy(
    context.connection,
    {
      conversationId,
      conversationParticipantId,
      tenantId,
    },
    {},
    { allowNull: true },
  );
  if (exists) {
    await ConversationItemLastRead.update(context.connection, {
      id: exists.id,
      tenantId,
      conversationId,
      conversationParticipantId,
      lastConversationItemId: last(conversationItemIds),
      conversationItemUnreadCount: 0,
      readAt: DateTime.utc(),
    });
  } else {
    await ConversationItemLastRead.create(context.connection, {
      tenantId,
      conversationId,
      conversationParticipantId,
      lastConversationItemId: last(conversationItemIds),
      conversationItemUnreadCount: 0,
      readAt: DateTime.utc(),
    });
  }

  await context.connection.commit();
  return true;
}
