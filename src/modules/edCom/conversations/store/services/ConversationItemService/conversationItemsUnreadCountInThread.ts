import { IContext } from 'edana-microservice';
import { map } from 'lodash';
import ConversationItem from '../../entities/ConversationItem';
import ConversationItemRead from '../../entities/ConversationItemRead';

interface IConversationItemsUnreadCountInThread {
  conversationItemsIds: number[];
  tenantId: number;
}

export default async function conversationItemsUnreadCountInThread(
  context: IContext,
  { tenantId, conversationItemsIds }: IConversationItemsUnreadCountInThread,
) {
  const groups = await context.connection.execute<{
    REPLIED_ITEM_ID: number;
    UNREAD_COUNT: number;
  }>(
    `
    Select ECI.REPLIED_ITEM_ID, count(*) as UNREAD_COUNT
from ${ConversationItem.table} ECI
    left join "${
      ConversationItemRead.table
    }" ECIR on ECI.ID = ECIR.CONVERSATION_ITEM_ID
where ECIR.ID IS NULL
    and ECI.TENANT_ID = :tenantId
    and ECI.REPLIED_ITEM_ID in (${conversationItemsIds.join(', ')})
group by ECI.REPLIED_ITEM_ID
    `,
    { tenantId },
  );

  return map(groups.rows, ({ REPLIED_ITEM_ID, UNREAD_COUNT }) => ({
    repliedItemId: REPLIED_ITEM_ID,
    unreadCount: UNREAD_COUNT,
  }));
}
