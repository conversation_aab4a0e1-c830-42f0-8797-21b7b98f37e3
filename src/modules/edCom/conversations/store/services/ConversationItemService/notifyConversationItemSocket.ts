import { IContext } from 'edana-microservice';
import { find, map, pick } from 'lodash';

import { SecureId } from '../../../../../../graphql/resolvers';
import { ACTIVE } from '../../../../../../model/Status';
import { getPerson } from '../../../../../core';
import { uniqMap } from '../../../../../core/helpers';
import ConversationParticipant from '../../entities/ConversationParticipant';
import ConversationItem, {
  IConversationItem,
} from '../../entities/ConversationItem';
import { ED_COM_CONVERSATION_ITEM_ATTACHMENTS } from '../../../fsCategoriesAreas';
import { OFFLINE, ONLINE } from '../../../../../../model/OnlineStatus';

export const notifyConversationItemSocket = async (
  context: IContext,
  { conversationItem, tenantId }: ISendConversationItemSocketParams,
): Promise<void> => {
  const recipients = await ConversationParticipant.loadBy(context.connection, {
    status: ACTIVE,
    conversationId: conversationItem.conversationId,
    tenantId,
  });

  const createdByParticipant = find(recipients, {
    id: conversationItem.conversationParticipantId,
  });

  if (!createdByParticipant) {
    return;
  }

  const person = await getPerson(context, {
    id: createdByParticipant.personId,
  });

  const quotedConversationItem = conversationItem.quotedItemId
    ? await getConversationItemCooked(
        context,
        await ConversationItem.findBy(context.connection, {
          id: conversationItem.quotedItemId,
        }),
      )
    : undefined;

  const client = await context.requireServiceClient('edana2_api_notf');

  const conversationItemCooked = await getConversationItemCooked(
    context,
    conversationItem,
  );

  const isOnlineDict = await client.users.getOnline({ userIds: [person.id] });

  const itemToDelivery = {
    conversationId: conversationItem.conversationId,
    conversationItem: {
      ...conversationItemCooked,
      quote: quotedConversationItem,
      person: {
        fullName: [person.firstName, person.middleName, person.lastName]
          .filter(n => !!n)
          .join(' '),
        ...pick(person, [
          'firstName',
          'lastName',
          'fullName',
          'gender',
          'organisationGroupId',
          'titleId',
          'tenantId',
        ]),
        isOnline: isOnlineDict[person.id] ? ONLINE : OFFLINE,
        photoFileId: SecureId.serialize(person.photoFileId),
        id: SecureId.serialize(person.id),
        __typename: 'Person',
      },

      recipients: uniqMap(recipients, 'personId').filter(
        x => x !== context.userId,
      ),
    },
  };

  try {
    await client.conversations.sendConversationItem(itemToDelivery);
  } catch (e) {
    context.log.error('Error sending new conversation item notification', e);
  }
};

const getConversationItemCooked = async (
  context: IContext,
  conversationItem: IConversationItem,
): Promise<object> => {
  const attachments = await (await context.fsClient).loadEntityAttachments(
    ED_COM_CONVERSATION_ITEM_ATTACHMENTS,
    conversationItem.id,
  );

  return {
    ...conversationItem,
    content: conversationItem.content
      ? await convertClobToString(conversationItem.content)
      : '',
    attachments: map(attachments, ({ fileId, ...rest }) => ({
      ...rest,
      fileId: SecureId.serialize(fileId),
      __typename: 'FileAttachment',
    })),
    __typename: 'ConversationItem',
  };
};

interface ISendConversationItemSocketParams {
  conversationItem: IConversationItem;
  tenantId: number;
}

async function convertClobToString(clob): Promise<string> {
  return new Promise<string>((resolve, reject) => {
    let result = '';
    clob.setEncoding('utf8');

    clob.on('data', (chunk: string) => {
      result += chunk;
    });

    clob.on('end', () => {
      resolve(result);
    });

    clob.on('error', (err: Error) => {
      reject(err);
    });
  });
}
