import { IAttachFileInfo, IContext, DateTime } from 'edana-microservice';
import ConversationItem from '../../entities/ConversationItem';
import u from './utils';
import ConversationParticipant from '../../entities/ConversationParticipant';
import { notifyConversationItemSocket } from './notifyConversationItemSocket';
import Conversation from '../../entities/Conversation';
import { ACTIVE } from '../../../../../../model/Status';
import ConversationItemLastRead from '../../entities/ConversationItemLastRead';

interface ICreateConversationItemArgs {
  tenantId: number;
  organisationGroupId: number;
  conversationId: number;
  userId: number;
  content: string;
  parentId?: number;
  attachments?: IAttachFileInfo[];
  quotedItemId?: number;
  quotedItemContent?: string;
}

export default async function createConversationItem(
  context: IContext,
  {
    tenantId,
    content,
    conversationId,
    userId,
    parentId,
    attachments,
    organisationGroupId,
    quotedItemContent,
    quotedItemId,
  }: ICreateConversationItemArgs,
) {
  const {
    id: conversationParticipantId,
  } = await ConversationParticipant.findBy(context.connection, {
    tenantId,
    personId: userId,
    conversationId,
  });

  const participants = await ConversationParticipant.loadBy(
    context.connection,
    {
      tenantId,
      conversationId,
      personId: { not: userId },
      status: ACTIVE,
    },
  );

  const conversationItem = await ConversationItem.create(context.connection, {
    tenantId,
    conversationParticipantId,
    createdAt: DateTime.utc(),
    updatedAt: DateTime.utc(),
    content,
    conversationId,
    parentId,
    quotedItemContent,
    quotedItemId,
  });

  for (const participant of participants) {
    const exists = await ConversationItemLastRead.findBy(
      context.connection,
      {
        conversationId,
        conversationParticipantId: participant.id,
        tenantId,
      },
      {},
      { allowNull: true },
    );
    if (exists) {
      await ConversationItemLastRead.update(context.connection, {
        id: exists.id,
        tenantId,
        conversationItemUnreadCount: exists.conversationItemUnreadCount + 1,
        readAt: DateTime.utc(),
      });
    } else {
      await ConversationItemLastRead.create(context.connection, {
        tenantId,
        conversationId,
        conversationParticipantId: participant.id,
        // TODO: Needed to be replaced with NULL
        lastConversationItemId: 3,
        conversationItemUnreadCount: 1,
        readAt: DateTime.utc(),
      });
    }
  }

  await u.manageConversationItemAttachments(context, {
    tenantId,
    organisationGroupId,
    attachments,
    conversationItemId: conversationItem.id,
  });

  await Conversation.update(context, {
    id: conversationId,
    tenantId,
    updatedAt: DateTime.utc(),
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    skipCheckAccess: true,
  });

  await context.connection.commit();

  await notifyConversationItemSocket(context, { tenantId, conversationItem });

  return await ConversationItem.findBy(context.connection, {
    id: conversationItem.id,
    tenantId,
  });
}
