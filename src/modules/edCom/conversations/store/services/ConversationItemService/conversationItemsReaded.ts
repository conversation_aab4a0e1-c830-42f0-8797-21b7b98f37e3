import { IContext } from 'edana-microservice';

import ConversationParticipant from '../../entities/ConversationParticipant';
import ConversationItemLastRead from '../../entities/ConversationItemLastRead';

interface IConversationItemRead {
  tenantId: number;
  conversationId: number;
  userId: number;
}

export default async function conversationItemsReaded(
  context: IContext,
  { tenantId, userId, conversationId }: IConversationItemRead,
) {
  const {
    id: conversationParticipantId,
  } = await ConversationParticipant.findBy(context.connection, {
    tenantId,
    personId: userId,
    conversationId,
  });

  const response = await ConversationItemLastRead.findBy(
    context.connection,
    {
      tenantId,
      conversationParticipantId,
      conversationId,
    },
    {},
    { allowNull: true },
  );

  return response;
}
