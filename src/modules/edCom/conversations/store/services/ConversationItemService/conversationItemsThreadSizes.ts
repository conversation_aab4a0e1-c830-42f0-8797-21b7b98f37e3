import { IContext } from 'edana-microservice';
import ConversationItem from '../../entities/ConversationItem';
import { ACTIVE } from '../../../../../../model/Status';

interface IConversationItemsThreadSizes {
  tenantId: number;
  conversationItemsIds: number[];
}

export default async function conversationItemsThreadSizes(
  context: IContext,
  { tenantId, conversationItemsIds }: IConversationItemsThreadSizes,
) {
  return await ConversationItem.bulkLoadCountBy(
    context.connection,
    'parentId',
    {
      parentId: conversationItemsIds,
      tenantId,
      status: ACTIVE,
    },
  );
}
