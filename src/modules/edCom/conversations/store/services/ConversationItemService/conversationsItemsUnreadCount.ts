import { isEmpty, map } from 'lodash';
import { IContext } from 'edana-microservice';

import ConversationParticipant from '../../entities/ConversationParticipant';
import ConversationItemLastRead from '../../entities/ConversationItemLastRead';

interface IConversationsItemsUnreadCount {
  tenantId: number;
  userId: number;
  conversationIds?: number[];
}

export default async function conversationsItemsUnreadCount(
  context: IContext,
  { tenantId, userId, conversationIds }: IConversationsItemsUnreadCount,
) {
  const conversationParticipants = await ConversationParticipant.loadBy(
    context.connection,
    {
      tenantId,
      personId: userId,
      ...(!isEmpty(conversationIds) ? { conversationId: conversationIds } : {}),
    },
  );
  const response = await ConversationItemLastRead.loadBy(context.connection, {
    tenantId,
    conversationParticipantId: map(conversationParticipants, 'id'),
    conversationId: map(conversationParticipants, 'conversationId'),
  });

  return map(response, ({ conversationItemUnreadCount, conversationId }) => ({
    unreadCount: conversationItemUnreadCount,
    conversationId,
  }));
}
