import { IContext, actionHelpers } from 'edana-microservice';
import { get } from 'lodash';

import ConversationItem from '../../entities/ConversationItem';
import Status from '../../../../../../model/Status';
import ConversationReactionType, {
  TConversationReactionMainType,
} from '../../../model/ConversationReactionType';
import ConversationItemReaction from '../../entities/ConversationItemReaction';
import ConversationParticipant from '../../entities/ConversationParticipant';

const { cookWhereClauses } = actionHelpers;

interface IConversationItems {
  tenantId: number;
  conversationId: number;
  parentId?: number | null;
  count: number;
  after: string;
  userId: number;
  reactionType?: TConversationReactionMainType;
  anchor?: number;
}

export default async function conversationItemsConnection(
  context: IContext,
  {
    tenantId,
    conversationId,
    parentId,
    count,
    after,
    reactionType,
    userId,
    anchor,
  }: IConversationItems,
) {
  const {
    id: conversationParticipantId,
  } = await ConversationParticipant.findBy(context.connection, {
    tenantId,
    personId: userId,
  });

  let itemsCountToAnchor = 0;

  const constraint = [
    {
      tenantId,
      conversationId,
      status: Status.ACTIVE,
    },
    parentId !== undefined ? { parentId } : null,
    reactionType
      ? `ID in (
            SELECT CONVERSATION_ITEM_ID 
            FROM "${ConversationItemReaction.table}"
            WHERE TENANT_ID = ${tenantId} 
                AND CONVERSATION_PARTICIPANT_ID = ${conversationParticipantId} 
                AND REACTION_TYPE = ${ConversationReactionType.mapping[reactionType]}
            )`
      : null,
  ];

  if (anchor) {
    const { params, clauses } = cookWhereClauses(
      ConversationItem.framework,
      constraint,
    );

    const anchorRes = await context.connection.execute<{ COUNT: number }>(
      `
        SELECT COUNT(*) COUNT      
        FROM ${ConversationItem.table} CI
        where ${[...clauses, 'ID >= :anchor'].join(' AND ')} 
      `,
      { ...params, anchor },
    );

    itemsCountToAnchor = get(anchorRes.rows, ['0', 'COUNT']) + 1;
  }

  const res = await ConversationItem.loadByCursor(
    context.connection,
    constraint,
    {
      count: Math.max(count, itemsCountToAnchor),
      after,
      order: {
        sortKey: 'id',
        sortOrder: 'DESC',
      },
    },
  );

  return res;
}
