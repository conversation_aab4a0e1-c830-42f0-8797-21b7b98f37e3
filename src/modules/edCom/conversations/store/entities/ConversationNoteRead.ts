import { Entity, IWIthId, IWithType } from 'edana-microservice';

export interface IConversationNoteRead extends IWIthId, IWithType {
  tenantId: number;
  conversationNoteId: number;
  conversationParticipantId: number;
}

const ConversationNoteRead = new Entity<IConversationNoteRead>(
  'ConversationNoteRead',
  {
    tenantId: 'number',
    conversationNoteId: 'number',
    conversationParticipantId: 'number',
  },
  {},
  {
    tableName: 'EC_CONVERSATION_NOTE_READ_old',
  },
);

export default ConversationNoteRead;
