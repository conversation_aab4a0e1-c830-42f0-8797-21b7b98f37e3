import {
  Entity,
  IConnection,
  IFramework,
  IWIthId,
  IWithType,
} from 'edana-microservice';

import { isEmpty, set } from 'lodash';

import ConversationReactionType, {
  TConversationReactionMainType,
} from '../../model/ConversationReactionType';
import ConversationParticipant from './ConversationParticipant';

export interface IConversationItemReaction extends IWIthId, IWithType {
  tenantId: number;
  conversationItemId: number;
  conversationParticipantId: number;
  reactionType: TConversationReactionMainType;
}

class ConversationItemReactionEntity extends Entity<IConversationItemReaction> {
  loadParticipantReactions: (
    connection: IConnection,
    params: {
      reactionType: TConversationReactionMainType;
      personId?: number;
      tenantId: number;
      conversationItemId: number[];
    },
  ) => Promise<IConversationItemReaction[]>;
}

const ConversationItemReaction = new ConversationItemReactionEntity(
  'ConversationItemReaction',
  {
    tenantId: 'number',
    conversationItemId: 'number',
    conversationParticipantId: 'number',
    reactionType: {
      mapping: ConversationReactionType.mapping,
    },
  },
  {
    loadParticipantReactions: async function (
      this: ConversationItemReactionEntity,
      { tableName, createInstance }: IFramework<IConversationItemReaction>,
      connection: IConnection,
      {
        reactionType,
        personId,
        tenantId,
        conversationItemId,
      }: {
        reactionType: TConversationReactionMainType;
        personId?: number;
        tenantId: number;
        conversationItemId: number[];
      },
    ): Promise<IConversationItemReaction[]> {
      if (isEmpty(conversationItemId)) {
        return [];
      }

      const clauses = [
        `CP.TENANT_ID = :tenantId`,
        `CIR.CONVERSATION_ITEM_ID IN  (${conversationItemId.join(',')})`,
        `CIR.REACTION_TYPE = :reactionType`,
      ];

      const params = {
        reactionType: ConversationReactionType.mapping[reactionType],
        tenantId,
      };

      if (personId) {
        clauses.push(`CP.PERSON_ID = :personId`);
        set(params, 'personId', personId);
      }

      const query = `
        SELECT CIR.* FROM "${tableName}" CIR 
        JOIN ${ConversationParticipant.table} CP
        ON CIR.CONVERSATION_PARTICIPANT_ID = CP.ID
        WHERE ${clauses.join(' AND ')}
      `;

      const { rows = [] } = await connection.execute(query, params);

      return rows.map(createInstance);
    },
  },
  {
    tableName: 'EC_CONVERSATION_ITEM_REACTION',
  },
);

export default ConversationItemReaction;
