import { Entity, IWIthId, IWithType } from 'edana-microservice';

export interface IConversationItemRead extends IWIthId, IWithType {
  tenantId: number;
  conversationItemId: number;
  conversationParticipantId: number;
}

const ConversationItemRead = new Entity<IConversationItemRead>(
  'ConversationItemRead',
  {
    tenantId: 'number',
    conversationItemId: 'number',
    conversationParticipantId: 'number',
  },
  {},
  {
    tableName: 'EC_CONVERSATION_ITEM_READ_old',
  },
);

export default ConversationItemRead;
