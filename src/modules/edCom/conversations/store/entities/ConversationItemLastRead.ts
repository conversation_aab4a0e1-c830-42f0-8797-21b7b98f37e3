import { Entity, IWIthId, IWithType, DateTime } from 'edana-microservice';

export interface IConversationItemLastRead extends IWIthId, IWithType {
  tenantId: number;
  conversationId: number;
  conversationParticipantId: number;
  lastConversationItemId: number;
  conversationItemUnreadCount: number;
  readAt: DateTime;
}

const ConversationItemLastRead = new Entity<IConversationItemLastRead>(
  'ConversationItemRead',
  {
    tenantId: 'number',
    conversationId: 'number',
    conversationParticipantId: 'number',
    lastConversationItemId: 'number',
    conversationItemUnreadCount: 'number',
    readAt: 'datetime',
  },
  {},
  {
    tableName: 'EC_CONVERSATION_ITEM_LAST_READ',
  },
);

export default ConversationItemLastRead;
