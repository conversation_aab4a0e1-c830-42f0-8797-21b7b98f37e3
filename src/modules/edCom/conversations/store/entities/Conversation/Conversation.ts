import {
  Entity,
  IConnection,
  IFramework,
  validators,
} from 'edana-microservice';
import ConversationStatus from '../../../model/ConversationStatus';
import checkAccess from './validators/checkAccess';
import ConversationTypePartCount from '../../../model/ConversationTypePartCount';

import { IConversation } from '../../../abstract';

export { IConversation };
const { tenantValidator } = validators;

type TChangeOwner = (
  connection: IConnection,
  params: {
    tenantId: number;
    ownerPersonId: number;
    id: number;
  },
) => Promise<IConversation>;

type TConversationAction = {
  changeOwner: TChangeOwner;
};

const Conversation = new Entity<IConversation, TConversationAction>(
  'Conversation',
  {
    tenantId: 'number',
    personEntityAllocationId: 'number',
    organisationId: 'number',
    topic: 'string',
    status: {
      mapping: ConversationStatus.mapping,
    },
    ownerPersonId: 'number',
    moduleAreaId: 'number',
    referenceId: 'number',
    dateFrom: 'date',
    dateTo: 'date',
    createdBy: 'number',
    createdAt: 'date',
    updatedBy: 'number',
    updatedAt: 'date',
    typePartCount: {
      mapping: ConversationTypePartCount.mapping,
    },
  },
  {
    changeOwner: async (
      frameWork: IFramework<IConversation>,
      connection: IConnection,
      { tenantId, ownerPersonId, id },
    ) => {
      await connection.execute(
        `
            UPDATE ${Conversation.table} 
            SET OWNER_PERSON_ID = :ownerPersonId
            WHERE TENANT_ID = :tenantId and id = :id
        `,
        { id, tenantId, ownerPersonId },
      );

      return await Conversation.findBy(connection, { tenantId, id });
    },
  },
  {
    tableName: 'EC_CONVERSATION',
  },
  {
    validators: [
      {
        actions: ['update'],
        validators: [
          {
            validate: tenantValidator,
            params: ({ id, tenantId }) => ({ id, tenantId }),
            errorMessage: 'tenantAccessError',
          },
          {
            validate: checkAccess,
            params: params => params,
          },
        ],
      },
    ],
  },
);

export default Conversation;
