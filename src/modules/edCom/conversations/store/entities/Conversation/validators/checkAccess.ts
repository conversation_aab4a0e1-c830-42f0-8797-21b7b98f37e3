import { IFramework, errors, actionHelpers } from 'edana-microservice';
import { some } from 'lodash';

const { HumanError } = errors;
const { cookWhereClauses } = actionHelpers;

import { IConversation } from '../../../../abstract';

const fields = [
  'id',
  'tenantId',
  'personEntityAllocationId',
  'organisationId',
  'topic',
  'status',
];

export default async function checkAccess(
  framework: IFramework<IConversation>,
  { currentItems, connection, args },
): Promise<void> {
  const { tableName, createInstance } = framework;
  const items = currentItems(...args);

  const { id, tenantId, userId, skipCheckAccess } = items;
  if (skipCheckAccess) return;
  const { clauses, params } = cookWhereClauses(framework, { tenantId, id });

  const { rows } = await connection.execute(
    `SELECT * 
    FROM ${tableName} 
    WHERE ${clauses.join(' AND ')}`,
    params,
  );

  if (rows) {
    const conversation = createInstance(rows[0]);
    if (
      conversation.ownerPersonId !== userId &&
      some(fields, key => conversation[key] !== items[key])
    ) {
      throw new HumanError(['accessDenied']);
    }
  }
}
