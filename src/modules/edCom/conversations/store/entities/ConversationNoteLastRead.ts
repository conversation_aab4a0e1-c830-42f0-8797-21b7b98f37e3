import { Entity, IWIthId, IWithType, DateTime } from 'edana-microservice';

export interface IConversationNoteRead extends IWIthId, IWithType {
  tenantId: number;
  conversationId: number;
  conversationParticipantId: number;
  lastConversationNoteId: number;
  conversationNoteUnreadCount: number;
  readAt: DateTime;
}

const ConversationNoteLastRead = new Entity<IConversationNoteRead>(
  'ConversationNoteLastRead',
  {
    tenantId: 'number',
    conversationId: 'number',
    conversationParticipantId: 'number',
    lastConversationNoteId: 'number',
    conversationNoteUnreadCount: 'number',
    readAt: 'datetime',
  },
  {},
  {
    tableName: 'EC_CONVERSATION_NOTE_LAST_READ',
  },
);

export default ConversationNoteLastRead;
