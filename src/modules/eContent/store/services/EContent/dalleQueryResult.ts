import { IContext } from 'edana-microservice';
import <PERSON><PERSON> from 'jimp';
import eContentSetting, { IEContentSettingParms } from './eContentSetting';
import { QUESTION } from '../../../../core/model/SettingType';

const dalleQueryResult = async (
  context: IContext,
  params: IDalleQueryResultParams,
): Promise<IDalleQueryResult> => {
  const completion = await getDalleResult(context, params);
  console.log('completion', completion);
  const base64 = await urlToBase64(completion.data?.[0]?.url || '');
  const result = {
    success: true,
    message: `successfulll result!`,
    response: base64,
  };
  return result;
};

export default dalleQueryResult;

export interface IDalleQueryResult {
  success: boolean;
  message: string;
  response: string;
  __type?: 'DalleQueryResult';
}

export interface IDalleQueryResultParams {
  version: string;
  searchQuery: string;
  tenantId: number;
  imageUrl: string;
}

const getRgbaPngBufferFromUrl = async (imageUrl: string): Promise<Buffer> => {
  try {
    // Step 1: Download the image
    const imageResponse = await fetch(imageUrl);
    const imageArrayBuffer = await imageResponse.arrayBuffer();
    const imageBuffer = Buffer.from(imageArrayBuffer);

    const image = await Jimp.read(response.data);
    image.rgba(true);
    return await image.getBufferAsync(Jimp.MIME_PNG);

    // Step 2: Convert to RGBA PNG in-memory
    // const rgbaBuffer = await sharp(imageBuffer)
    //   .ensureAlpha() // Add alpha channel if not present
    //   .png() // Convert to PNG
    //   .toBuffer(); // Return as buffer

    // return rgbaBuffer;
  } catch (error) {
    console.error('Error processing image:', error.message);
    throw error;
  }
};

const urlToBase64 = async (url: string) => {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`Failed to fetch URL: ${response.statusText}`);
  }

  const imageResponse = await fetch(url);
  const arrayBuffer = await imageResponse.arrayBuffer();
  const base64 = Buffer.from(arrayBuffer).toString('base64');

  return `data:${contentType};base64,${base64}`;
};

const getDalleResult = async (
  context: IContext,
  params: IDalleQueryResultParams,
) => {
  const { imageUrl, searchQuery } = params;
  const settingParams: IEContentSettingParms = {
    settingType: QUESTION,
    searchQuery: 'OPENAI_API_KEY',
  };
  const settingList = await eContentSetting(context, settingParams);
  const OPENAI_API_KEY =
    settingList?.response[0].replace('OPENAI_API_KEY=', '').trim() || '';
  console.log('OPENAI_API_KEY', OPENAI_API_KEY);

  const form = new FormData();

  // // Download image from URL and pass it directly to OpenAI
  // const imageResponse = await fetch(imageUrl);
  // if (!imageResponse.ok) {
  //   throw new Error(
  //     `Failed to fetch image from URL: ${imageResponse.statusText}`,
  //   );
  // }

  // // Get the image as a stream/buffer
  // const imageArrayBuffer = await imageResponse.arrayBuffer();
  // const imageBuffer = Buffer.from(imageArrayBuffer);

  // Try to convert the image to ensure it has proper format for OpenAI
  // const processedImageBuffer = await ensureRGBAFormat(imageBuffer);

  const rgbaBuffer = await getRgbaPngBufferFromUrl(imageUrl);
  // Create a blob with proper MIME type for FormData
  const imageBlob = new Blob([rgbaBuffer], { type: 'image/png' });

  // Append with filename to help OpenAI recognize it as an image file
  form.append('image', imageBlob, 'image.png');
  form.append('prompt', searchQuery);
  form.append('n', '1');
  form.append('size', '1024x1024');
  form.append('response_format', 'b64_json');

  const response = await fetch('https://api.openai.com/v1/images/edits', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${OPENAI_API_KEY}`,
    },
    body: form,
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`OpenAI error: ${error}`);
  }

  return response.json();
};