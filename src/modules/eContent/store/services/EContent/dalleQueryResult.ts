
import { IContext } from 'edana-microservice';
import eContentSetting, { IEContentSettingParms } from './eContentSetting';
import { QUESTION } from '../../../../core/model/SettingType';

const dalleQueryResult = async (
  context: IContext,
  params: IDalleQueryResultParams,
): Promise<IDalleQueryResult> => {
  const completion = await getDalleResult(context, params);
  console.log('completion', completion);
  const base64 = await urlToBase64(completion.data?.[0]?.url || '');
  const result = {
    success: true,
    message: `successfulll result!`,
    response: base64,
  };
  return result;
};

export default dalleQueryResult;

export interface IDalleQueryResult {
  success: boolean;
  message: string;
  response: string;
  __type?: 'DalleQueryResult';
}

export interface IDalleQueryResultParams {
  version: string;
  searchQuery: string;
  tenantId: number;
  imageUrl: string;
}

const urlToBase64 = async (url: string) => {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`Failed to fetch URL: ${response.statusText}`);
  }

  const contentType = response.headers.get('content-type');
  const arrayBuffer = await response.arrayBuffer();
  const base64 = Buffer.from(arrayBuffer).toString('base64');

  return `data:${contentType};base64,${base64}`;
};

const getDalleResult = async (
  context: IContext,
  params: IDalleQueryResultParams,
) => {
  const { imageUrl, searchQuery } = params;
  const settingParams: IEContentSettingParms = {
    settingType: QUESTION,
    searchQuery: 'OPENAI_API_KEY',
  };
  const settingList = await eContentSetting(context, settingParams);
  const OPENAI_API_KEY =
    settingList?.response[0].replace('OPENAI_API_KEY=', '').trim() || '';

  const form = new FormData();

  // Download image from URL and convert to buffer
  const imageResponse = await fetch(imageUrl);
  if (!imageResponse.ok) {
    throw new Error(
      `Failed to fetch image from URL: ${imageResponse.statusText}`,
    );
  }

  const imageBuffer = Buffer.from(await imageResponse.arrayBuffer());
  const contentType = imageResponse.headers.get('content-type') || 'image/png';

  // Create a blob from the buffer for FormData
  const imageBlob = new Blob([imageBuffer], { type: contentType });

  form.append('image', imageBlob, 'image.png');
  form.append('prompt', searchQuery);
  form.append('n', '1');
  form.append('size', '1024x1024');
  form.append('response_format', 'b64_json');

  const response = await fetch('https://api.openai.com/v1/images/edits', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${OPENAI_API_KEY}`,
    },
    body: form,
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`OpenAI error: ${error}`);
  }

  return response.json();
};
