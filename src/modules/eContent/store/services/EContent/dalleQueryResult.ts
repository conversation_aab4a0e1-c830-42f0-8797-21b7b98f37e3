import { IContext } from 'edana-microservice';
import eContentSetting, { IEContentSettingParms } from './eContentSetting';
import { QUESTION } from '../../../../core/model/SettingType';

const dalleQueryResult = async (
  context: IContext,
  params: IDalleQueryResultParams,
): Promise<IDalleQueryResult> => {
  const completion = await getDalleResult(context, params);
  console.log('completion', completion);
  const base64 = await urlToBase64(completion.data?.[0]?.url || '');
  const result = {
    success: true,
    message: `successfulll result!`,
    response: base64,
  };
  return result;
};

export default dalleQueryResult;

export interface IDalleQueryResult {
  success: boolean;
  message: string;
  response: string;
  __type?: 'DalleQueryResult';
}

export interface IDalleQueryResultParams {
  version: string;
  searchQuery: string;
  tenantId: number;
  imageUrl: string;
}

const urlToBase64 = async (url: string) => {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`Failed to fetch URL: ${response.statusText}`);
  }

  const contentType = response.headers.get('content-type');
  const arrayBuffer = await response.arrayBuffer();
  const base64 = Buffer.from(arrayBuffer).toString('base64');

  return `data:${contentType};base64,${base64}`;
};

const getDalleResult = async (
  context: IContext,
  params: IDalleQueryResultParams,
) => {
  const { imageUrl, searchQuery } = params;
  const settingParams: IEContentSettingParms = {
    settingType: QUESTION,
    searchQuery: 'OPENAI_API_KEY',
  };
  const settingList = await eContentSetting(context, settingParams);
  const OPENAI_API_KEY =
    settingList?.response[0].replace('OPENAI_API_KEY=', '').trim() || '';

  const form = new FormData();

  // Download image from URL and pass it directly to OpenAI
  const imageResponse = await fetch(imageUrl);
  if (!imageResponse.ok) {
    throw new Error(
      `Failed to fetch image from URL: ${imageResponse.statusText}`,
    );
  }

  // Get the image as a stream/buffer
  const imageArrayBuffer = await imageResponse.arrayBuffer();
  const imageBuffer = Buffer.from(imageArrayBuffer);

  // Try to convert the image to ensure it has proper format for OpenAI
  const processedImageBuffer = await ensureRGBAFormat(imageBuffer);

  // Create a blob with proper MIME type for FormData
  const imageBlob = new Blob([processedImageBuffer], { type: 'image/png' });

  // Append with filename to help OpenAI recognize it as an image file
  form.append('image', imageBlob, 'image.png');
  form.append('prompt', searchQuery);
  form.append('n', '1');
  form.append('size', '1024x1024');
  form.append('response_format', 'b64_json');

  const response = await fetch('https://api.openai.com/v1/images/edits', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${OPENAI_API_KEY}`,
    },
    body: form,
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`OpenAI error: ${error}`);
  }

  return response.json();
};

const ensureRGBAFormat = async (imageBuffer: Buffer): Promise<Buffer> => {
  // Check if the image is a PNG by examining the magic bytes
  const isPNG =
    imageBuffer.length >= 8 &&
    imageBuffer[0] === 0x89 &&
    imageBuffer[1] === 0x50 &&
    imageBuffer[2] === 0x4e &&
    imageBuffer[3] === 0x47;

  if (!isPNG) {
    throw new Error(
      'Image must be in PNG format. Please provide a PNG image URL.',
    );
  }

  // For PNG files, we need to check if they have an alpha channel
  // This is a simplified approach - we'll try to modify the PNG to ensure RGBA
  try {
    // Look for the IHDR chunk to check color type
    const ihdrIndex = imageBuffer.indexOf('IHDR');
    if (ihdrIndex === -1) {
      throw new Error('Invalid PNG file - no IHDR chunk found');
    }

    // Color type is at offset 9 from IHDR
    const colorTypeOffset = ihdrIndex + 9;
    if (colorTypeOffset >= imageBuffer.length) {
      throw new Error('Invalid PNG file - incomplete IHDR chunk');
    }

    const colorType = imageBuffer[colorTypeOffset];

    // Color types: 0=grayscale, 2=RGB, 3=palette, 4=grayscale+alpha, 6=RGBA
    if (colorType === 6) {
      // Already RGBA, return as-is
      return imageBuffer;
    } else if (colorType === 2) {
      // RGB format - this is likely the issue
      // For now, let's try a workaround by creating a simple RGBA PNG
      // This is a simplified approach that may not work for all images
      throw new Error(
        'Image is in RGB format but OpenAI requires RGBA. Please use an image editing tool to convert the image to RGBA format (PNG with transparency support).',
      );
    } else {
      // Other formats
      throw new Error(
        `Unsupported PNG color type: ${colorType}. OpenAI requires RGBA format (color type 6).`,
      );
    }
  } catch (error) {
    // If we can't parse the PNG properly, return the original and let OpenAI handle it
    console.warn('PNG analysis failed, trying original image:', error.message);
    return imageBuffer;
  }
};
