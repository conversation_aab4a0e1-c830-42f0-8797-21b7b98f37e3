import fs from 'fs';
import path from 'path';

import { IContext } from 'edana-microservice';
import eContentSetting, { IEContentSettingParms } from './eContentSetting';
import { QUESTION } from '../../../../core/model/SettingType';

const dalleQueryResult = async (
  context: IContext,
  params: IDalleQueryResultParams,
): Promise<IDalleQueryResult> => {
  const completion = await getDalleResult(context, params);
  const base64 = await urlToBase64(completion.data?.[0]?.url || '');
  const result = {
    success: true,
    message: `successfulll result!`,
    response: base64,
  };
  return result;
};

export default dalleQueryResult;

export interface IDalleQueryResult {
  success: boolean;
  message: string;
  response: string;
  __type?: 'DalleQueryResult';
}

export interface IDalleQueryResultParams {
  version: string;
  searchQuery: string;
  tenantId: number;
}

const urlToBase64 = async (url: string) => {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`Failed to fetch URL: ${response.statusText}`);
  }

  const contentType = response.headers.get('content-type');
  const arrayBuffer = await response.arrayBuffer();
  const base64 = Buffer.from(arrayBuffer).toString('base64');

  return `data:${contentType};base64,${base64}`;
};

const getDalleResult = async (
  context: IContext,
  params: IDalleQueryResultParams,
) => {
  const { imageUrl, searchQuery } = params;
  const settingParams: IEContentSettingParms = {
    settingType: QUESTION,
    searchQuery: 'OPENAI_API_KEY',
  };
  const settingList = await eContentSetting(context, settingParams);
  const OPENAI_API_KEY =
    settingList?.response[0].replace('OPENAI_API_KEY=', '').trim() || '';

  const form = new FormData();

  // imageUrl is image url
  
  form.append('image', fs.createReadStream(imageUrl));

  form.append('prompt', searchQuery);
  form.append('n', '1');
  form.append('size', '1024x1024');
  form.append('response_format', 'url');

  const response = await fetch('https://api.openai.com/v1/images/edits', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${OPENAI_API_KEY}`,
    },
    body: form as any,
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`OpenAI error: ${error}`);
  }

  return response.json();
};
