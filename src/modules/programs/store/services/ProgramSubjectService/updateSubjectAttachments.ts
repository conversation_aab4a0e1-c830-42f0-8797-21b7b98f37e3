import { IContext } from 'edana-microservice';
import IAttachFileInfo from '../../../../../graphql/abstract/IAttachFileInfo';

export const SUBJECT_ATTACHMENTS = 'subject-attachments-area';

export default async function (
  context: IContext,
  subjectId,
  {
    subjectAttachments = [],
    tenantId,
    organisationGroupId,
  }: {
    subjectAttachments: IAttachFileInfo[];
    tenantId: number;
    organisationGroupId: number;
  },
) {
  const fsClient = await context.fsClient;

  await fsClient.updateEntityAttachments(
    SUBJECT_ATTACHMENTS,
    subjectId,
    subjectAttachments,
    { tenantId, organisationGroupId },
  );
}
