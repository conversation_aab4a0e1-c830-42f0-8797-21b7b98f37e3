import { createBatchResolver } from 'graphql-resolve-batch';
import { map, keyBy } from 'lodash';
import { IContext } from 'edana-microservice';

import bulkFirstAttachment from './utils/bulkFirstAttachment';
import { ED_COM_CONVERSATION_ATTACHMENTS } from '../../fsCategoriesAreas';
import participantsCountEndpoint from '../../modules/edCom/conversations/endpoints/conversationList/participantsCount';
import bulkPerson from './utils/bulkPerson';
import getLastConversationItem from '../../modules/edCom/conversations/endpoints/conversationList/lastConversationItem';
import { uniqMap } from '../../modules/core/helpers';
import conversationsItemsUnreadCount from '../../modules/edCom/conversations/endpoints/conversationItem/conversationsItemsUnreadCount';
import conversationsNotesUnreadCount from '../../modules/edCom/conversations/endpoints/conversationNote/conversationsNotesUnreadCount';
import { IConversation } from '../../modules/edCom/conversations/abstract';
import { getOrganisationName } from '../../modules/core/store/services/OrganisationsService/edComOrganisationSelector';

export const logoAttachment = bulkFirstAttachment(
  ED_COM_CONVERSATION_ATTACHMENTS,
);

export const participantsCount = createBatchResolver(
  async (conversations, {}, context: IContext) => {
    const ids = map(conversations, 'id');

    const counts = await participantsCountEndpoint(context, {
      tenantId: context.tenantId,
      conversationId: ids,
    });

    const countsHash = keyBy(counts, 'groupId');

    return map(conversations, ({ id }) => countsHash[id]?.count || 0);
  },
);

export const ownerPerson = bulkPerson('ownerPersonId');

export const createdByPerson = bulkPerson('createdBy');

export const lastConversationItem = createBatchResolver(
  async (nodes, args: undefined, context: IContext) => {
    const { tenantId } = context;

    const lastMessages = await getLastConversationItem(context, {
      tenantId,
      conversationIds: uniqMap(nodes),
    });

    const lastMessagesHash = keyBy(lastMessages, 'messageId');

    return map(nodes, ({ id }) => lastMessagesHash[id]);
  },
);

export const unreadConversationItemCount = createBatchResolver(
  async (conversations, {}, context: IContext) => {
    const conversationIds = map(conversations, 'id');
    const res = await conversationsItemsUnreadCount(context, {
      tenantId: context.tenantId,
      userId: context.userId,
      conversationIds,
    });

    const conversationsItemsUnreadCountHash = keyBy(res, 'conversationId');

    return map(
      conversations,
      ({ id }) => conversationsItemsUnreadCountHash[id]?.unreadCount || 0,
    );
  },
);

export const unreadConversationNotesCount = createBatchResolver(
  async (conversations, {}, context: IContext) => {
    const conversationIds = map(conversations, 'id');
    const res = await conversationsNotesUnreadCount(context, {
      tenantId: context.tenantId,
      userId: context.userId,
      conversationIds,
    });

    const conversationsNotesUnreadCountHash = keyBy(res, 'conversationId');

    return map(
      conversations,
      ({ id }) => conversationsNotesUnreadCountHash[id]?.unreadCount || 0,
    );
  },
);

//TODO implement
export const conversationArea = createBatchResolver(
  async (conversations, {}, context: IContext) => {
    return map(conversations, ({ moduleAreaId }) =>
      moduleAreaId ? 0 : 'Ed-com Conversation',
    );
  },
);

export const organisationAllocationNames = createBatchResolver(
  async (nodes: IConversation[], args: undefined, context: IContext) => {
    const { tenantId } = context;

    const allocationPromises = nodes.map(async node => {
      const { organisationId } = node;

      const allocationNamesResponse = await getOrganisationName(
        context.connection,
        {
          organisationId: organisationId as number,
          tenantId,
        },
      );

      return allocationNamesResponse;
    });

    const allocationNamesResults = await Promise.all(allocationPromises);

    const organisationsMapped = keyBy(allocationNamesResults, 'id');

    const mapped = map(nodes, ({ organisationId }) => ({
      organisationId: organisationId,
      organisationName: organisationId
        ? organisationsMapped[organisationId]?.name || ''
        : '',
      allocationsNames: [],
    }));
    return mapped;
  },
);
