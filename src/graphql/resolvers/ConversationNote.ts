import { createBatchResolver } from 'graphql-resolve-batch';
import { map, keyBy, get } from 'lodash';
import { IContext } from 'edana-microservice';

import conversationItemsPersons from '../../modules/edCom/conversations/endpoints/conversationItem/conversationItemsPersons';

import bulkAttachments from './utils/bulkAttachments';
import { ED_COM_CONVERSATION_NOTE_ATTACHMENTS } from '../../fsCategoriesAreas';
import conversationNotesReaded from '../../modules/edCom/conversations/endpoints/conversationNote/conversationNotesReaded';

export const person = createBatchResolver(
  async (conversationItems, args, context: IContext) => {
    const persons = await conversationItemsPersons(context, {
      conversationParticipantIds: map(
        conversationItems,
        'conversationParticipantId',
      ),
      tenantId: context.tenantId,
    });

    const personsHash = keyBy(persons, 'conversationParticipantId');

    return map(
      conversationItems,
      row => personsHash[row['conversationParticipantId']],
    );
  },
);

export const attachments = bulkAttachments(
  ED_COM_CONVERSATION_NOTE_ATTACHMENTS,
);

export const isReaded = createBatchResolver(
  async (items, {}, context: IContext) => {
    const readed = await conversationNotesReaded(context, {
      tenantId: context.tenantId,
      userId: context.userId,
      conversationId: get(items, '0.conversationId'),
    });
    return map(
      items,
      ({ id }) => !!readed && id <= readed?.lastConversationNoteId,
    );
  },
);
