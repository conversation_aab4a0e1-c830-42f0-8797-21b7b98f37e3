import { createBatchResolver } from 'graphql-resolve-batch';
import { map, groupBy, keyBy, get, filter, isEmpty } from 'lodash';
import { IContext } from 'edana-microservice';

import { LIKE, STAR } from '../../model/ConversationReactionType';

import conversationItemsReactions from '../../modules/edCom/conversations/endpoints/conversationItem/conversationItemsReactions';
import conversationItemsPersons from '../../modules/edCom/conversations/endpoints/conversationItem/conversationItemsPersons';
import conversationItemsThreadSizes from '../../modules/edCom/conversations/endpoints/conversationItem/conversationItemsThreadSizes';
import conversationItemsReaded from '../../modules/edCom/conversations/endpoints/conversationItem/conversationItemsReaded';
import conversationItems from '../../modules/edCom/conversations/endpoints/conversationItem/conversationItems';

import bulkAttachments from './utils/bulkAttachments';
import { ED_COM_CONVERSATION_ITEM_ATTACHMENTS } from '../../fsCategoriesAreas';

export const isStarred = createBatchResolver(
  async (conversationItems, {}, context: IContext) => {
    const conversationItemsIds = map(conversationItems, 'id');

    const starredReactions = await conversationItemsReactions(context, {
      tenantId: context.tenantId,
      conversationItemId: conversationItemsIds,
      reactionType: STAR,
      personId: context.userId,
    });

    const starredReactionsHash = groupBy(
      starredReactions,
      'conversationItemId',
    );

    return map(
      conversationItems,
      ({ id }) => !!starredReactionsHash[id]?.length,
    );
  },
);

export const isLiked = createBatchResolver(
  async (conversationItems, {}, context: IContext) => {
    const conversationItemsIds = map(conversationItems, 'id');

    const likedReactions = await conversationItemsReactions(context, {
      tenantId: context.tenantId,
      conversationItemId: conversationItemsIds,
      reactionType: LIKE,
      personId: context.userId,
    });

    const likedReactionsHash = groupBy(likedReactions, 'conversationItemId');

    return map(conversationItems, ({ id }) => !!likedReactionsHash[id]?.length);
  },
);

export const likeCount = createBatchResolver(
  async (conversationItems, {}, context: IContext) => {
    const conversationItemsIds = map(conversationItems, 'id');

    const reactions = await conversationItemsReactions(context, {
      tenantId: context.tenantId,
      conversationItemId: conversationItemsIds,
      reactionType: LIKE,
    });

    const reactionsHash = groupBy(reactions, 'conversationItemId');

    return map(conversationItems, ({ id }) => (reactionsHash[id] || []).length);
  },
);

export const reactions = createBatchResolver(
  async (conversationItems, {}, context: IContext) => {
    const conversationItemsIds = map(conversationItems, 'id');

    const reactions = await conversationItemsReactions(context, {
      tenantId: context.tenantId,
      conversationItemId: conversationItemsIds,
      reactionType: LIKE,
      personId: context.userId,
    });

    const reactionsHash = groupBy(reactions, 'conversationItemId');

    return map(conversationItems, ({ id }) => reactionsHash[id] || []);
  },
);

export const person = createBatchResolver(
  async (conversationItems, args, context: IContext) => {
    const persons = await conversationItemsPersons(context, {
      conversationParticipantIds: map(
        conversationItems,
        'conversationParticipantId',
      ),
      tenantId: context.tenantId,
    });

    const personsHash = keyBy(persons, 'conversationParticipantId');

    return map(
      conversationItems,
      row => personsHash[row['conversationParticipantId']],
    );
  },
);

export const threadSize = createBatchResolver(
  async (conversationItems, args, context: IContext) => {
    const threads = filter(conversationItems, { parentId: null });

    if (isEmpty(threads)) {
      return map(conversationItems, () => 0);
    }

    const threadSizes = await conversationItemsThreadSizes(context, {
      tenantId: context.tenantId,
      conversationItemsIds: map(threads, 'id'),
    });

    const threadSizesHash = keyBy(threadSizes, 'groupId');

    return map(conversationItems, ({ id }) => threadSizesHash[id]?.count || 0);
  },
);

export const unreadCount = createBatchResolver(
  async (conversationItems, args, context) => {
    const threads = filter(conversationItems, { parentId: null });

    if (isEmpty(threads)) {
      return map(conversationItems, () => 0);
    }
    return map(conversationItems, () => 0);

    // const countGroup = await conversationItemsUnreadCountInThread(context, {
    //   tenantId: context.tenantId,
    //   conversationItemsIds: map(threads, 'id'),
    // });

    // const countGroupHash = keyBy(countGroup, 'repliedItemId');

    // return map(
    //   conversationItems,
    //   ({ id }) => countGroupHash[id]?.unreadCount || 0,
    // );
  },
);

export const attachments = bulkAttachments(
  ED_COM_CONVERSATION_ITEM_ATTACHMENTS,
);

export const isReaded = createBatchResolver(
  async (items, {}, context: IContext) => {
    const readed = await conversationItemsReaded(context, {
      tenantId: context.tenantId,
      userId: context.userId,
      conversationId: get(items, '0.conversationId'),
    });

    return map(
      items,
      ({ id }) => readed && id <= readed.lastConversationItemId,
    );
  },
);

export const quote = createBatchResolver(
  async (items, {}, context: IContext) => {
    const _conversationItems = await conversationItems(context, {
      tenantId: context.tenantId,
      ids: map(items, 'id'),
    });

    const conversationItemsHash = keyBy(_conversationItems, 'id');

    return map(items, ({ id }) => conversationItemsHash[id]);
  },
);
