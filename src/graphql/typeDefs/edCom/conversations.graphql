enum ConversationSearchMode {
    MESSAGES
    TOPIC
}

enum ConversationStatus {
    ACTIVE
    HIDDEN
    READ_ONLY
    DELETED
}

enum ConversationContactType {
    CONTACTS
    MY_CONVERSATION
    CONVERSATION_PARTICIPATED
}

enum ConversationItemReactionType {
    LIKE
    UNLIKE
    STAR
    UNSTAR
}

enum ConversationItemReactionMainType {
    LIKE
    STAR
}

enum ConversationItemReportReason {
    SPAM
    VIOLENCE
    INAPPROPRIATE
    OTHER
}

enum ConversationTypePartCount {
    GROUP
    ONE_TO_ONE
}


input ConversationInput {
    topic: String!
    status: ConversationStatus!
    dateFrom: DateTime
    dateTo: DateTime
    organisationId: Int!
    personEntityAllocationId: Int!
    typePartCount: ConversationTypePartCount!

    logoAttachment: AttachFileInfo
    participantsAdded: [SecureId!]!
    participantsRemoved: [SecureId!]!
}

input ConversationItemInput {
    conversationId: Int!
    content: String!
    parentId: Int
    attachments: [AttachFileInfo]
    quotedItemId: Int
    quotedItemContent: String
}


type Conversation {
    id: Int!
    personEntityAllocationId: Int
    organisationId: Int
    topic: String!
    status: ConversationStatus!
    moduleIcon: String
    conversationArea: String
    dateFrom: DateTime
    dateTo: DateTime
    ownerPersonId: SecureId!
    typePartCount: ConversationTypePartCount!

    createdAt: DateTime!
    updatedAt: DateTime!

    # graphql object resolver,
    participantsCount: Int!
    logoAttachment: FileAttachment
    ownerPerson: Person!
    createdByPerson: Person!
    updatedByPerson: Person!
    lastConversationItem: ConversationItem
    unreadConversationItemCount: Int!
    unreadConversationNotesCount: Int!

    organisationAllocationNames: OrganisationAllocationNames
}

type ConversationNote {
    id: Int!
    conversationId: Int!
    conversationParticipantId: Int!
    content: String!
    createdAt: DateTime!
    updatedAt: DateTime!

    person: Person!
    attachments: [FileAttachment]
    isReaded: Boolean!
}

type ConversationItem {
    id: Int!
    conversationId: Int!
    conversationParticipantId: Int!
    content: String
    parentId: Int
    createdAt: DateTime!
    updatedAt: DateTime!
    quotedItemId: Int
    quotedItemContent: String

    # graphql object resolver,
    isStarred: Boolean!
    isLiked: Boolean!
    isReaded: Boolean!
    reactions: [ConversationItemReaction!]!
    likeCount: Int!
    threadSize: Int!
    unreadCount: Int!
    person: Person!
    attachments: [FileAttachment]
    quote: ConversationItem
}

type ConversationItemReaction {
    id: Int!
    conversationItemId: Int!
    conversationParticipantId: Int!
    reactionType: ConversationItemReactionType!

}

type ConversationItemReport {
    id: Int!
    conversationItemId: Int!
    conversationParticipantId: Int!
    reason: ConversationItemReactionType!
    description: String
}

type ConversationParticipant {
    id: Int!

    person: Person!
}

type ConversationItemsConnection {
    pageInfo: PageInfo!
    edges: [ConversationItemEdge]!
}

type ConversationNotesConnection {
    pageInfo: PageInfo!
    edges: [ConversationNoteEdge]!
}

type ConversationItemEdge {
    node: ConversationItem!
    cursor: String!
}

type ConversationNoteEdge {
    node: ConversationNote!
    cursor: String!
}

extend type Query {
    # get messaging channles
    conversations(
        first: Int = 0
        count: Int = 10
        searchQuery: String = ""
        searchQueryMode: [ConversationSearchMode]
        organisationIds: [Int!]
        lastActivity: Date
    ): [Conversation!]!

    conversationItem(id:Int!): ConversationItem!

    conversationItemsConnection(
        conversationId: Int!
        reactionType: ConversationItemReactionMainType
        parentId: Int
        count: Int = 10,
        after: String,
        anchor: Int
    ): ConversationItemsConnection!

    conversationNotesConnection(
        conversationId: Int!
        count: Int = 10,
        after: String ): ConversationNotesConnection!

    conversation(id: Int!): Conversation!

    conversationAttachments(
        id: Int!,
        first: Int = 0,
        count: Int = 10
    ): [FileAttachment]

    conversationAttachmentsCount(id: Int!): Int!

    conversationsCount(
        searchQuery: String = ""
        searchQueryMode: [ConversationSearchMode]
        organisationIds: [Int!]
        lastActivity: Date
    ): Int!

    conversationParticipants(
        conversationId: Int!
        first: Int = 0
        count: Int = 10
    ): [ConversationParticipant]
    conversationParticipantsCount(conversationId: Int!): Int!

    conversationContacts(
        first: Int = 0
        count: Int = 10

        searchQuery: String = ""
        contactTypes: [ConversationContactType]!
        personEntityTypeIds: [Int]!
    ): [Person]


    conversationContactsCount(
        searchQuery: String = ""
        contactTypes: [ConversationContactType]!
        personEntityTypeIds: [Int]!
    ): Int!

    conversationsItemsUnreadCount: Int!

    conversationOneToOne(personId: SecureId!): Conversation

}

extend type Mutation {
    updateConversation(id: Int! params: ConversationInput!): Conversation!
    createConversation(params: ConversationInput!): Conversation!
    deleteConversation(id: Int!, isHardDelete: Boolean = false): DeleteResponse!
    leaveConversation(id: Int!): Boolean!

    changeConversationOwner(params: ChangeConversationOwnerInput! ): Conversation!

    reactConversationItem(
        params: ConversationItemReactionInput!
    ): ConversationItem!

    readConversationItem(params: ConversationItemReadInput!): Boolean!

    reportConversationItem(params: ConversationItemReportInput!): Boolean!


    updateConversationItem(id: Int! params: ConversationItemInput!): ConversationItem!
    createConversationItem(params: ConversationItemInput!): ConversationItem!
    deleteConversationItem(id: Int!): DeleteResponse!
    deleteConversationItemAttachment(conversationItemId: Int! attachmentId: Int!): DeleteResponse!

    createConversationNote(params: ConversationNoteInput!): ConversationNote!
    updateConversationNote(id: Int! params: ConversationNoteInput!): ConversationNote!
    deleteConversationNote(id: Int! ): DeleteResponse!

    readConversationNote(params: ConversationNoteReadInput!): Boolean!

}

input ConversationNoteInput {
    conversationId: Int!
    content: String!
    attachments: [AttachFileInfo]
}

input ChangeConversationOwnerInput {
    ownerPersonId: SecureId!,
    conversationId: Int!
}

input ConversationItemReactionInput {
    conversationItemId: Int!
    reactionType: ConversationItemReactionType!
}

input ConversationItemReadInput {
    conversationItemIds: [Int!]!
}

input ConversationNoteReadInput {
    conversationNoteIds: [Int!]!
}

input ConversationItemReportInput {
    conversationItemId: Int!
    reason: ConversationItemReportReason!
    description: String
}
