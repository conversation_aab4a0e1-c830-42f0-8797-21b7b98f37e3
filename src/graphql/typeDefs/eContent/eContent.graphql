extend type Query {
  eContentItem(id: Int!): EContentItem!
  eContentItems(
    resourceId: [Int!]!
    languageId: [Int!]
    status: [StatusWithDraft]
    first: Int = 0
    count: Int = 10
    searchQuery: String
    sortKey: String
    sortOrder: String
    page: String = ""
  ): [EContentItem!]!
  eContentItemsByIds(
    itemId: [Int!]!
    resourceId: [Int!]!
    languageId: [Int!]
    status: [StatusWithDraft]
    first: Int = 0
    count: Int = 10
    searchQuery: String
    sortKey: String
    sortOrder: String
    tableName: String
    lsLpTaskId: Int
    lpTaskId: Int
  ): [EContentItem!]!
  eContentItemsByResourceIds(resourceId: [Int!]!): [EContentItem!]!
  eContentItemsByIdsCount(
    itemId: [Int!]!
    resourceId: [Int!]!
    languageId: [Int!]
    status: [StatusWithDraft]
    searchQuery: String
  ): Int!
  eContentItemsCount(
    resourceId: [Int!]!
    languageId: [Int!]
    status: [StatusWithDraft]
    searchQuery: String
    page: String = ""
  ): Int!
  eContentItemQuestion(id: Int!): EContentQuestion!
  eContentItemContent(id: Int!): EContentContent!
  eContentItemContents(
    itemId: Int!
    languageId: [Int!]
    status: [StatusWithDraft]
    first: Int = 0
    count: Int = 10
    searchQuery: String
    sortOrder: SortOrderType!
  ): [EContentContent!]!
  eContentItemPreviousNextContents(
    contentId: Int!
    itemId: Int!
    languageId: [Int!]
    status: [StatusWithDraft]
    first: Int = 0
    count: Int = 10
    searchQuery: String
    sortOrder: SortOrderType!
    hasContentIndex: Boolean!
  ): [EContentContent!]!
  eContentItemContentsCount(
    itemId: Int!
    languageId: [Int!]
    status: [StatusWithDraft]
    searchQuery: String
  ): Int!
  eContentItemContentsSequence(
    itemId: Int!
    languageId: [Int!]
    status: [StatusWithDraft]
    searchQuery: String
  ): EContentContentSequence
  eContentItemsTranslationSupportLanguages(
    itemId: Int!
    first: Int
    count: Int
  ): [EContentItemsTranslationSupportLanguages]
  eContentItemsTranslationSupportLanguagesCount(itemId: Int!): Int!
  eContentClobsSummary(
    itemId: Int!
    languageId: Int!
    attributeId: Int
    first: Int = 0
    count: Int = 5
  ): [EContentClobSummary]
  eContentClobsJson(itemId: Int!): [EContentClobJson]
  eContentClobsCompare(
    itemId: Int!
    languageId: [Int!]
    attributeId: Int
    first: Int = 0
    count: Int = 5
  ): [EContentClobCompare]
  eContentClobsCompareCount(
    itemId: Int!
    languageId: [Int!]
    attributeId: Int
  ): EContentClobsCompareCount
  eContentClobsSearch(
    searchQuery: String
    itemId: Int!
    languageId: [Int!]
    attributeId: Int
    first: Int = 0
    count: Int = 5
  ): [EContentClobSearch]
  eContentResourceXLanguages(
    resourceId: Int!
    itemId: Int
  ): [EContentResourceXLanguages]
  eContentContentView(
    resourceId: [Int!]!
    languageId: [Int!]
    status: [StatusWithDraft]
    first: Int = 0
    count: Int = 10
    searchQuery: String
    sortKey: String
    sortOrder: String
  ): [EContentContent!]!
  eContentContentLsView(
    ecnItemId: [Int!]!
    resourceId: [Int!]!
    languageId: [Int!]
    status: [StatusWithDraft]
    first: Int = 0
    count: Int = 10
    searchQuery: String
    sortKey: String
    sortOrder: String
  ): [EContentContent!]!
  eContentContentLsViewCount(
    ecnItemId: [Int!]!
    resourceId: [Int!]!
    languageId: [Int!]
    status: [StatusWithDraft]
    searchQuery: String
  ): Int!
  eContentContentViewByIds(
    itemId: [Int!]!
    ecnItemId: [Int!]!
    resourceId: [Int!]!
    languageId: [Int!]
    status: [StatusWithDraft]
    first: Int = 0
    count: Int = 10
    searchQuery: String
    sortKey: String
    sortOrder: String
    tableName: String
    lsLpTaskId: Int
    lpTaskId: Int
  ): [EContentContent!]!
  eContentContentViewCount(
    resourceId: [Int!]!
    languageId: [Int!]
    status: [StatusWithDraft]
    searchQuery: String
  ): Int!
  eContentContentViewByIdsCount(
    itemId: [Int!]!
    ecnItemId: [Int!]!
    resourceId: [Int!]!
    languageId: [Int!]
    status: [StatusWithDraft]
    searchQuery: String
  ): Int!

  openaiQueryResult(
    version: String!
    searchQuery: String!
    type: String!
  ): OpenaiQueryResult!

  dalleQueryResult(
    version: String
    searchQuery: String!
    imageUrl: String!
    type: String
  ): DalleQueryResult!

  geminiQueryResult(
    version: String
    searchQuery: String!
    type: String!
  ): GeminiQueryResult!

  googleTranslateResult(
    content: String!
    source: String!
    target: String!
    format: String!
  ): GoogleTranslateResult!

  eContentSetting(
    settingType: SettingType!
    searchQuery: String
    count: Int
  ): EContentSettingResult!
  getEContentLogs(
    contentId: Int
    searchQuery: String
    logTypeIds: [Int!]!
    startDate: DateTime!
    endDate: DateTime!
    first: Int = 0
    count: Int = 10
    sortKey: String = "createdAt"
    sortOrder: String = "DESC"
  ): [EContentLog]!
  getEContentLogsCount(
    contentId: Int
    logTypeIds: [Int!]!
    searchQuery: String
    startDate: DateTime!
    endDate: DateTime!
  ): Int!
}

enum SettingType {
  QUESTION
  TRANSFORM
  GPT_VERSION
  IMAGE_AI_IMPROVEMENT
}
enum SortOrderType {
  ALPHABETICAL
  RECENTALY_UPDATED
  SEQUENCE
}
extend type Mutation {
  createEContentItem(params: EContentItemInput!): EContentItem!
  updateEContentItem(id: Int!, params: EContentItemInput!): EContentItem!
  deleteEContentItem(id: Int!): DeleteResponse!
  createEContentItemContent(params: EContentContentInput!): EContentContent!
  updateEContentItemContent(
    id: Int!
    params: EContentContentInput!
  ): EContentContent!
  moveEContentItemContent(
    id: Int!
    itemId: Int!
    resourceId: Int!
  ): EContentContent!

  updateEContentItemQuestion(
    id: Int!
    params: EContentQuestionInput!
  ): EContentQuestion!
  updateEContentItemQuestions(
    id: Int!
    params: EContentQuestionInput!
  ): EContentQuestion!
  deleteEContentQuestion(id: Int!, sequence: Int!): EContentQuestion!
  moveEContentQuestion(
    id: Int!
    sequenceFrom: Int!
    sequenceTo: Int!
  ): EContentQuestion!
  moveEContentSequence(
    id: Int!
    sequenceFrom: String!
    sequenceTo: String!
  ): Boolean!
  createEContentLog(params: EContentLogInput!): EContentLog!
  updateEContentLog(id: Int!, params: EContentLogInput!): EContentLog!
  deleteEContentLog(id: Int!): DeleteResponse!
}
input MoveEContentSequenceIncoming {
  id: Int!
  sequence: Int!
}
type EContentResourceXLanguages {
  id: Int
  resourceId: Int
  languageId: Int
  status: Status

  contentsCount: Int
  updatedByPerson: Person
  resource: EContentResource
  languages: [Language]
}
type EContentItemsTranslationSupportLanguages {
  id: Int
  name: String
  count: Int
  nonUniqueLanguageContent: Boolean
  nonUniqueGroupSequence: Boolean
  heading: [String]
}
type EContentItemsTranslationSupportLanguagesCount {
  count: Int
}
type EContentItem {
  id: Int
  pltId: Int
  resourceId: Int
  name: String
  description: String
  createdBy: SecureId
  createdAt: DateTime
  updatedBy: SecureId
  updatedAt: DateTime
  status: StatusWithDraft

  contentsCount: Int
  updatedByPerson: Person
  resource: EContentResource
  languages: [Language]
  sequence: Int
}

input EContentItemInput {
  id: Int
  resourceId: Int!
  name: ClearedString!
  description: ClearedString
  status: StatusWithDraft = ACTIVE
}

type EContentQuestion {
  id: Int
  clobs: [EContentClob]
  questionOptions: [EContentQuestionOptions]
}
input EContentQuestionInput {
  id: Int
  clobs: [EContentClobInput]
  questionOptions: [EContentQuestionOptionsInput]
}

type EContentContent {
  id: Int
  pltId: Int
  itemId: Int
  resourceLanguageId: Int
  description: String
  createdBy: SecureId
  createdAt: DateTime
  updatedBy: SecureId
  updatedAt: DateTime
  status: StatusWithDraft
  sequence: Float

  updatedByPerson: Person
  clobs: [EContentClob]
  questionOptions: [EContentQuestionOptions]
  language: Language
  item: EContentItem
}
type EContentContentSequence {
  contentCount: Int
  minSequence: Float
  maxSequence: Float
}
type OpenaiQueryResult {
  success: Boolean
  message: String
  response: [String]!
}

type DalleQueryResult {
  success: Boolean
  message: String
  response: String!
}
type GeminiQueryResult {
  success: Boolean
  message: String
  response: [String]!
}
type GoogleTranslateResult {
  success: Boolean!
  message: String!
  response: String!
}
type EContentLog {
  id: Int
  tenantId: Int!
  personId: Int!
  logTypeId: Int!
  description: String!
  createdAt: DateTime!

  user: Person
}
input EContentLogInput {
  contentId: Int!
  logTypeId: Int!
  description: String!
}
type EContentSettingResult {
  success: Boolean
  message: String
  response: [String]!
  count: Int
}

input EContentContentInput {
  id: Int
  itemId: Int!
  description: ClearedString
  status: StatusWithDraft
  sequence: Float

  languageId: Int!
  resourceId: Int!
  clobs: [EContentClobInput]
  questionOptions: [EContentQuestionOptionsInput]
}

type EContentClob {
  id: Int
  resourceId: Int
  itemId: Int
  contentId: Int
  languageId: Int
  resourceAttributeId: Int
  content: String
  groupSequence: Int
  contentClobTypeId: Int
  resourceCategoryId: Int

  attachments: [FileAttachment]
}

type EContentClobSummary {
  sequence: Float
  id: Int
  resourceId: Int
  itemId: Int
  contentId: Int
  languageId: Int
  resourceAttributeId: Int
  content: String
  groupSequence: Int
  attachments: [FileAttachment]
}

type EContentClobJson {
  sequenceId: Int
  languageId: Int
  heading: String
  textContent: String
}

type EContentClobCompare {
  sequence: Float
  id: Int
  resourceId: Int
  itemId: Int
  contentId: Int
  languageId: Int
  resourceAttributeId: Int
  content: String
  groupSequence: Int
  attachments: [FileAttachment]
}

type EContentClobsCompareCount {
  count: Int
}

type EContentClobSearch {
  sequence: Float
  id: Int
  resourceId: Int
  itemId: Int
  contentId: Int
  languageId: Int
  resourceAttributeId: Int
  content: String
  groupSequence: Int
  attachments: [FileAttachment]
}

input EContentClobInput {
  id: Int
  resourceId: Int
  itemId: Int
  contentId: Int
  languageId: Int
  resourceAttributeId: Int
  content: ClearedString = ""
  groupSequence: Int
  contentClobTypeId: Int
  resourceCategoryId: Int

  attachments: [AttachFileInfo]
}

type EContentQuestionOptions {
  id: Int
  contentId: Int
  contentClobId: Int
  description: String
  correctAnswer: String
  point: Int
  sequence: Int
  attachments: [FileAttachment]
}

input EContentQuestionOptionsInput {
  id: Int
  contentId: Int
  contentClobId: Int
  description: String
  correctAnswer: Int
  point: Int
  sequence: Int
  groupSequence: Int
  attachments: [AttachFileInfo]
}
