{"name": "edana2_api", "version": "0.0.0", "description": "edana 2 api", "main": "./src/index.ts", "engines": {"node": "16.20.2"}, "scripts": {"start": "cross-env NODE_OPTIONS=--max-old-space-size=4096 TS_NODE_FILES=true node $NODE_DEBUG_OPTION -r ts-node/register -r tsconfig-paths/register -r source-map-support/register ./src/index.ts", "dev": "cross-env NODE_PATH=./ ts-node-dev -r tsconfig-paths/register --respawn --transpile-only ./src/index.ts", "debug": "cross-env TS_NODE_FILES=true nodemon --config nodemon-debug-config.json $NODE_DEBUG_OPTION", "lint": "cross-env TIMING=1 eslint --ext .js,.ts --ignore-path .gitignore ./src", "lint:quiet": "cross-env TIMING=1 eslint --quiet --ext .js,.ts --ignore-path .gitignore ./src", "lint:fix": "cross-env TIMING=1 eslint --ext .js,.ts --max-warnings=0 --ignore-path .gitignore --fix ./src", "test": "mocha --exit --require ts-node/register/transpile-only -r tsconfig-paths/register src/**/*.spec.*", "test:coverage": "nyc --reporter=html mocha --exit --require ts-node/register './test/**/*.spec.js'", "migrate": "node src/db/migrate", "build": "tsc", "build:dev": "npm run build", "build:stage": "npm run build", "build:prod": "npm run build", "build:prod2": "npm run build", "ts-validate": "tsc --noEmit", "build-js-for-profile": "tsc --project tsconfig-profile.json", "copyfiles": "copyfiles --all ./src/graphql/typeDefs/*.graphql ./src/graphql/typeDefs/**/*.graphql ./src/endpoints/graphql/*.js ./dist", "start-built-js": "node ./dist/src/index.js", "clinic": "clinic flame -- node ./dist/src/index.js", "bubbleprof": "clinic bubbleprof -- node ./dist/src/index.js", "build-js-and-run": "npm run build-js-for-profile && npm run copyfiles && npm run start-built-js", "build-js-and-clinic": "npm run build-js-for-profile && npm run copyfiles && npm run clinic", "build-js-and-bubbleprof": "npm run build-js-for-profile && npm run copyfiles && npm run bubbleprof", "prepare": "husky install"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "mocha": {"spec": ["src/modules/**/test/**/*.spec.js", "test/**/*.spec.js"]}, "repository": {"type": "git", "url": "************************:ed-admin/edana2_api.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://gitlab2.ed-space.net/ed-admin/edana2_api/issues"}, "homepage": "https://gitlab2.ed-space.net/ed-admin/edana2_api", "dependencies": {"@types/express": "4.17.13", "@types/express-serve-static-core": "4.17.28", "apollo-cache-inmemory": "1.1.12", "apollo-client": "2.2.8", "apollo-link-context": "1.0.8", "apollo-link-http": "1.5.4", "apollo-server-koa": "1.3.6", "atob": "2.1.2", "btoa": "1.2.1", "bull": "3.29.3", "cookie": "0.3.1", "cross-env": "7.0.2", "dotenv": "8.1.0", "edana-microservice": "git+https://gitlab2.ed-space.net/ed-admin/edana-microservice#6.8.2", "edana_api_fs": "git+https://gitlab2.ed-space.net/ed-admin/edana_api_fs#3.8.22", "exceljs": "^4.3.0", "form-data": "4.0.3", "glob": "7.1.2", "google-id-token-verifier": "0.2.3", "gql-query-builder": "3.5.0", "graphql": "15.3.0", "graphql-resolve-batch": "1.0.2", "graphql-tools": "5.0.0", "ioredis": "^4.17.3", "joi": "17.6.0", "jsonwebtoken": "8.2.1", "koa": "2.5.0", "koa-body": "2.5.0", "koa-bodyparser": "4.2.0", "koa-i18n": "2.1.0", "koa-locale": "1.3.0", "koa-passport": "4.0.1", "koa-requestid": "2.0.1", "koa-route": "3.2.0", "lodash": "4.17.21", "luxon": "1.24.1", "mock-require": "3.0.3", "mongoose": "5.10.6", "nodemailer": "6.10.1", "oracledb": "5.1.0", "passport-azure-ad": "3.0.12", "passport-local": "1.0.0", "request": "2.88.2", "request-received": "0.0.3", "ts-node": "10.9.2", "typescript": "4.5.5", "ua-parser-js": "0.7.28", "uuid": "3.2.1", "winston": "2.4.2", "xlsx-stream-reader": "1.1.1", "xml2js": "0.6.2"}, "devDependencies": {"@commitlint/cli": "13.0.0", "@commitlint/config-conventional": "13.0.0", "@types/bull": "3.14.2", "@types/chai": "4.2.11", "@types/chai-as-promised": "7.1.3", "@types/graphql": "14.2.3", "@types/koa": "2.11.3", "@types/koa-passport": "4.0.2", "@types/koa-route": "3.2.4", "@types/lodash": "4.14.150", "@types/luxon": "1.24.1", "@types/node": "13.13.9", "@types/nodemailer": "6.4.16", "@types/sinon": "7.5.0", "@types/ua-parser-js": "0.7.36", "@typescript-eslint/eslint-plugin": "5.12.1", "@typescript-eslint/parser": "5.12.1", "babel-eslint": "8.2.3", "branch-name-lint": "2.1.1", "chai": "4.2.0", "chai-as-promised": "7.1.1", "copyfiles": "2.4.1", "eslint": "7.1.0", "eslint-config-edana-node-ts": "git+https://gitlab2.ed-space.net/ed-admin/eslint-config-edana-node-ts.git#21.1.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-no-only-tests": "2.4.0", "husky": "^8.0.0", "lint-staged": "10.2.6", "mocha": "6.2.1", "nock": "11.3.6", "nodemon": "2.0.4", "nyc": "14.1.1", "prettier": "2.2.1", "pretty-quick": "2.0.1", "sinon": "7.5.0", "ts-node-dev": "2.0.0"}, "overrides": {"types-ramda": "0.29.4", "@types/express-serve-static-core": "4.17.28", "@types/express": "4.17.13"}, "resolutions": {"ioredis": "4.28.5", "@types/ioredis": "4.28.10", "@typescript-eslint/eslint-plugin": "5.12.1", "@typescript-eslint/parser": "5.12.1", "mongoose": "6.13.5", "nodemailer": "6.4.16", "minimist": "0.2.4", "flat": "5.0.1", "path-to-regexp": "3.3.0", "body-parser": "1.20.3", "braces": "3.0.3", "ssh2": "1.4.0", "lodash": "4.17.21", "mpath": "0.5.1", "cross-spawn": "6.0.6", "jsonwebtoken": "9.0.0", "luxon": "1.28.1", "ua-parser-js": "0.7.33", "minimatch": "3.0.5"}, "serviceDependencies": {"edana2_api_notf": "2.0.7", "edana_api_fs": "3.8.22"}, "lint-staged": {"*.{js,ts}": "eslint --cache"}}